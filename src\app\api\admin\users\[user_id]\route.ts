import { NextRequest } from 'next/server';
import { forwardRequestToBackend } from '@/lib/serverFetch';

export async function GET(req: NextRequest, context: unknown) {
  const user_id = (context as { params?: { user_id?: string } })?.params?.user_id as string;
  return forwardRequestToBackend(req, `/admin/users/${user_id}`);
}

export async function DELETE(req: NextRequest, context: unknown) {
  const user_id = (context as { params?: { user_id?: string } })?.params?.user_id as string;
  return forwardRequestToBackend(req, `/admin/users/${user_id}`);
}

