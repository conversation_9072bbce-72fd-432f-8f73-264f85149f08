import type { Metadata } from 'next';
import '../globals.css';
import Header from '@/components/Header';
import QueryProvider from '@/lib/queryClient';

export const metadata: Metadata = {
  title: 'ABA Client Portal',
  description: 'ABA Portal UI',
};

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <Header />
          <main className="mx-auto max-w-6xl p-4">
            {children}
          </main>
        </QueryProvider>
      </body>
    </html>
  );
}

