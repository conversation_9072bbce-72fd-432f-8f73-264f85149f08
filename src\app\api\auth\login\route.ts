import { NextRequest, NextResponse } from 'next/server';
import { API_BASE_URL } from '@/lib/serverFetch';
import { setAuthCookieOnResponse } from '@/lib/auth';
import type { TokenResponse } from '@/lib/types';

export async function POST(req: NextRequest) {
  const body = await req.json();

  const res = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify(body),
  });

  if (!res.ok) {
    const text = await res.text();
    return new NextResponse(text || 'Login failed', { status: res.status });
  }

  const data = (await res.json()) as TokenResponse;
  const response = NextResponse.json(data);
  setAuthCookieOnResponse(response, data.access_token);
  return response;
}

